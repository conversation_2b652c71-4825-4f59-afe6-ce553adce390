# Mobile Development Setup

This guide helps you set up the Laravel middleware server to work with React Native apps on Android emulators and iOS simulators.

## Quick Start (Out of the Box!)

### 1. First-time setup
```bash
./middleware setup
```

This automatically:
- Creates your `.env` file from `.env.example`
- Detects your computer's hostname (e.g., `denali.local`)
- Configures <PERSON><PERSON> redirect URI to use your hostname
- Works for any developer on any machine!

### 2. Start mobile development server
```bash
./middleware mobile-dev
```

This will:
- Start the Docker containers with mobile development configuration
- Start the Laravel server accessible from mobile devices
- Display your hostname that mobile devices can connect to

### Custom port
```bash
./middleware mobile-dev 3000  # Use port 3000 instead of 8000
```

## React Native Configuration

Your React Native app should use the **IP address** approach for maximum compatibility.

Update your `getMiddlewareUrl` function:

```javascript
import { Platform } from 'react-native';

const getMiddlewareUrl = () => {
  if (__DEV__) {
    if (Platform.OS === 'android') {
      // Android emulator: use special host IP that points to localhost
      return `http://********:8000/auth/login`;
    } else {
      // iOS simulator: can use localhost directly
      return `http://localhost:8000/auth/login`;
    }
  } else {
    // In production, use your production URL
    return 'https://your-production-middleware.com/auth/login';
  }
};
```

## How It Works

1. **Hostname Detection**: Automatically detects each developer's computer hostname (e.g., `denali.local`)
2. **mDNS Resolution**: Uses `.local` domains that work automatically on iOS, Android, and web browsers
3. **Zero Configuration**: Each developer runs `./middleware setup` once and everything works
4. **Docker Integration**: Uses your existing Docker setup with mobile development configuration
5. **Universal Compatibility**: Works on macOS, Linux, and Windows - no IP address hardcoding needed
6. **Out of the Box**: New developers can clone, run setup, and start developing immediately

## Troubleshooting

### Android Emulator Can't Connect
- Make sure your Laravel server is running with `--host=0.0.0.0`
- Check that your React Native app is using the correct IP and port
- Verify your firewall isn't blocking port 8000

### iOS Simulator Can't Connect
- Same steps as Android emulator
- iOS Simulator uses your host machine's network, so the same IP should work

### Okta Callback Issues (ERR_NAME_NOT_RESOLVED)
This is automatically handled! The system detects when you're accessing from a mobile device and:
- Uses your actual IP address (e.g., `************:8000`) instead of `middleware.local`
- Configures Okta to redirect back to the correct IP address
- No manual configuration needed

### Different Port
If you need to use a different port:
```bash
./middleware mobile-dev 3000  # Uses port 3000 instead
```

### Manual IP Detection
If automatic IP detection fails, you can find your IP manually:

**macOS:**
```bash
ifconfig | grep "inet " | grep -v 127.0.0.1
```

**Linux:**
```bash
hostname -I
```

The script will still work - it just won't display the correct IP in the output.

## Docker Configuration

The mobile development setup uses a Docker Compose override file (`docker-compose.mobile-dev.yaml`) that:
- Exposes port 8000 from the workspace container to your host machine
- Allows customization of the port via the `MOBILE_DEV_PORT` environment variable

## Team Development (Out of the Box!)

Each new developer should:
1. **Clone the repo**
2. **Run `./middleware setup`** (one-time setup)
3. **Run `./middleware mobile-dev`** (start development server)
4. **Update React Native** to use hostname approach (see above)

That's it! No IP addresses to configure, no environment-specific setup needed.

### Why This Works

- **mDNS/Bonjour**: `.local` hostnames work automatically on iOS, Android, and web browsers
- **Automatic Detection**: Each developer's hostname is detected automatically
- **Zero Configuration**: No hardcoded IPs, no manual network setup
- **Universal Compatibility**: Works on any developer's machine, any network

### Example Flow

Developer "Alice" on machine `alice-macbook.local`:
1. Runs `./middleware setup` → Configures `http://alice-macbook.local:8000/sso-auth/callback`
2. Runs `./middleware mobile-dev` → Server accessible at `alice-macbook.local:8000`
3. Mobile devices connect to `alice-macbook.local:8000` → Everything works!

Developer "Bob" on machine `bob-laptop.local`:
1. Same commands → Automatically uses `http://bob-laptop.local:8000/sso-auth/callback`
2. No conflicts, no configuration needed!
