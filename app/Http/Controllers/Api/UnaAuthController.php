<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\UnaApiService;
use App\Services\UserSessionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class UnaAuth<PERSON>ontroller extends Controller
{
    protected $unaApiService;

    protected $userSessionService;

    public function __construct(UnaApiService $unaApiService, UserSessionService $userSessionService)
    {
        $this->unaApiService = $unaApiService;
        $this->userSessionService = $userSessionService;
    }

    public function login(Request $request)
    {
        $email = $request->input('email');

        if (! $email) {
            return response()->json(['error' => 'Email is required'], 400);
        }

        Log::info("🚀 Dev Login: Starting for email: {$email}");

        // Check if valid session exists first
        $user = $this->userSessionService->getUserSession($email);

        if ($user) {
            Log::info('♻️ Using existing valid session - skipping UNA API call');

            return response()->json([
                'message' => 'Using cached session',
                'email' => $email,
                'session_data' => [
                    'session_id' => $user->session_id,
                    'account_id' => $user->account_id,
                    'profile_id' => $user->profile_id,
                    'content_id' => $user->content_id,
                    'expires_at' => $user->expires_at,
                ],
            ]);
        }

        Log::info('🌐 No valid session found - calling UNA API');

        // Call UNA API and log the response
        $unaResponse = $this->unaApiService->loginToUna($email);

        Log::info('📥 Dev Login: UNA Response received', ['response' => $unaResponse]);

        // If UNA response is successful, store user data
        if ($unaResponse) {
            Log::info('✅ UNA API call successful - storing user data');

            $storedUserData = $this->userSessionService->storeUserData($email, $unaResponse);

            Log::info('📋 STORED USER DATA:', [
                'email' => $storedUserData->email,
                'session_id' => $storedUserData->session_id,
                'account_id' => $storedUserData->account_id,
                'profile_id' => $storedUserData->profile_id,
                'content_id' => $storedUserData->content_id,
                'updated_at' => $storedUserData->updated_at,
            ]);
        } else {
            Log::error('❌ UNA API call failed - no data to store');
        }

        return response()->json([
            'message' => 'UNA API call completed',
            'email' => $email,
            'una_response' => $unaResponse,
        ]);
    }

    public function logout(Request $request)
    {
        $email = $request->input('email');

        if (! $email) {
            return response()->json(['error' => 'Email is required'], 400);
        }

        Log::info("🚪 Logout initiated for: {$email}");

        // Call UNA logout API first
        $unaLogoutResponse = $this->unaApiService->logoutFromUna($email);

        if ($unaLogoutResponse) {
            Log::info('✅ UNA logout successful');
        } else {
            Log::warning('⚠️ UNA logout failed, proceeding with local cleanup');
        }

        // Clear Sanctum tokens (if user is authenticated)
        if ($request->user()) {
            $request->user()->tokens()->delete();
        }

        // Invalidate local session
        $this->userSessionService->invalidateSession($email);

        return response()->json([
            'message' => 'Logged out successfully',
            'una_logout_response' => $unaLogoutResponse,
        ]);
    }

    public function user(Request $request)
    {
        return response()->json($request->user());
    }

    public function checkSession(Request $request)
    {
        $email = $request->input('email');

        if (! $email) {
            return response()->json(['error' => 'Email is required'], 400);
        }

        Log::info("🔍 Checking session status for: {$email}");

        $user = $this->userSessionService->getUserSession($email);

        return response()->json([
            'email' => $email,
            'session_exists' => $user !== null,
            'is_expired' => $user === null,
            'session_data' => $user ? [
                'session_id' => $user->session_id,
                'account_id' => $user->account_id,
                'profile_id' => $user->profile_id,
                'content_id' => $user->content_id,
                'expires_at' => $user->expires_at,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
            ] : null,
        ]);
    }
}
