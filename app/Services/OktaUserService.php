<?php

namespace App\Services;

use App\Models\User;

class OktaUserService
{
    /**
     * Upsert a User from an Okta profile payload.
     *
     * @param array $oktaProfile Decoded userinfo response from Okta
     * @return User
     * @throws \InvalidArgumentException when required fields are missing
     */
    public function upsertUserFromProfile(array $oktaProfile): User
    {
        $email = $oktaProfile['email'] ?? null;
        if (empty($email)) {
            throw new \InvalidArgumentException('Email is required from Okta profile');
        }

        return User::updateOrCreate(
            ['okta_user_id' => $oktaProfile['sub'] ?? null],
            [
                'name' => $oktaProfile['name'] ?? ($oktaProfile['preferred_username'] ?? 'Unknown'),
                'email' => $email,
                'okta_email' => $oktaProfile['email'] ?? null,
                'okta_profile_data' => $oktaProfile,
                'email_verified_at' => (isset($oktaProfile['email_verified']) && $oktaProfile['email_verified']) ? now() : null,
            ]
        );
    }
}

