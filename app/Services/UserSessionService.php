<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class UserSessionService
{
    public function storeUserData(string $email, array $unaResponse): User
    {
        Log::info("📝 Starting user data storage for email: {$email}");

        // Extract data from UNA response (data is nested under 'data' key)
        $data = $unaResponse['data'] ?? [];
        $sessionId = $data['session'] ?? null;
        $accountId = $data['account_id'] ?? null;
        $profileId = $data['profile_id'] ?? null;
        $contentId = $data['content_id'] ?? null;

        // Calculate expiration time using UNA_SESSION_TTL from config
        $sessionTtl = (int) config('una.session_ttl', 1440); // Default 24 hours
        $expiresAt = now()->addMinutes($sessionTtl);

        Log::info('🔍 Extracted data from UNA response', [
            'session_id' => $sessionId,
            'account_id' => $accountId,
            'profile_id' => $profileId,
            'content_id' => $contentId,
            'ttl_minutes' => $sessionTtl,
            'expires_at' => $expiresAt->toISOString(),
        ]);

        // Store or update user session data
        $user = User::updateOrCreate(
            ['email' => $email],
            [
                'account_id' => $accountId,
                'profile_id' => $profileId,
                'content_id' => $contentId,
                'expires_at' => $expiresAt,
            ]
        );

        // Set session_id separately since it's not in fillable for security
        $user->session_id = $sessionId;
        $user->save();

        Log::info('💾 User data stored successfully', [
            'email' => $user->email,
            'session_id' => $user->session_id,
            'account_id' => $user->account_id,
            'profile_id' => $user->profile_id,
            'content_id' => $user->content_id,
            'expires_at' => $user->expires_at,
            'created_at' => $user->created_at,
            'updated_at' => $user->updated_at,
        ]);

        Log::info('📋 STORED USER DATA:', [
            'email' => $user->email,
            'session_id' => $user->session_id,
            'account_id' => $user->account_id,
            'profile_id' => $user->profile_id,
            'content_id' => $user->content_id,
            'updated_at' => $user->updated_at,
        ]);

        return $user;
    }

    /**
     * Check if a user session is expired and log the result
     */
    public function isSessionExpired(string $email): bool
    {
        $user = User::where('email', $email)->first();

        if (! $user) {
            Log::info("⚠️ No session found for email: {$email}");

            return true;
        }

        // Check if session was invalidated (logged out)
        if (! $user->session_id) {
            Log::info("🚪 Session invalidated (logged out) for: {$email}");

            return true;
        }

        $now = now();
        $isExpired = $now->gt($user->expires_at);

        Log::info('🕐 Session expiration check', [
            'email' => $email,
            'current_time' => $now->toISOString(),
            'expires_at' => $user->expires_at->toISOString(),
            'is_expired' => $isExpired,
            'time_remaining' => $isExpired ? 'EXPIRED' : $user->expires_at->diffForHumans($now),
        ]);

        if ($isExpired) {
            Log::warning("⏰ Session EXPIRED for {$email} - expired at {$user->expires_at}");
        } else {
            Log::info("✅ Session VALID for {$email} - expires in {$user->expires_at->diffForHumans($now)}");
        }

        return $isExpired;
    }

    /**
     * Get user session with expiration check
     */
    public function getUserSession(string $email): ?User
    {
        $user = User::where('email', $email)->first();

        if (! $user) {
            Log::info("❌ No session record found for: {$email}");

            return null;
        }

        // Check if session was invalidated (logged out)
        if (! $user->session_id) {
            Log::info("🚪 Session invalidated (logged out) for: {$email}");

            return null;
        }

        // Check expiration
        $now = now();
        $isExpired = $now->gt($user->expires_at);

        Log::info('🕐 Session expiration check', [
            'email' => $email,
            'current_time' => $now->toISOString(),
            'expires_at' => $user->expires_at->toISOString(),
            'is_expired' => $isExpired,
            'time_remaining' => $isExpired ? 'EXPIRED' : $user->expires_at->diffForHumans($now),
        ]);

        if ($isExpired) {
            Log::warning("⏰ Session EXPIRED for {$email} - expired at {$user->expires_at}");

            return null; // Return null for expired sessions
        }

        Log::info("✅ Session VALID for {$email} - expires in {$user->expires_at->diffForHumans($now)}");

        return $user;
    }

    /**
     * Invalidate user session on explicit logout
     */
    public function invalidateSession(string $email): bool
    {
        $user = User::where('email', $email)->first();

        if (! $user) {
            Log::info("⚠️ No session to invalidate for: {$email}");

            return false;
        }

        // Clear session_id but preserve user data for analytics
        $user->update(['session_id' => null]);

        Log::info('🚪 Session invalidated on logout', [
            'email' => $email,
            'account_id' => $user->account_id,
            'profile_id' => $user->profile_id,
            'invalidated_at' => now()->toISOString(),
        ]);

        return true;
    }
}
