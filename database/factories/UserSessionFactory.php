<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\OktaUserSession as UserSession;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OktaUserSession>
 */
class UserSessionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserSession::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'okta_access_token' => $this->faker->uuid(),
            'okta_refresh_token' => $this->faker->uuid(),
            'okta_id_token' => $this->faker->uuid(),
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', $this->faker->uuid()),
            'platform' => $this->faker->randomElement(['mobile', 'web']),
            'okta_session_id' => $this->faker->uuid(),
            'okta_user_data' => [
                'sub' => $this->faker->uuid(),
                'email' => $this->faker->email(),
                'name' => $this->faker->name(),
            ],
            'is_active' => true,
            'last_activity_at' => now(),
        ];
    }

    /**
     * Indicate that the session is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the session is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'okta_expires_at' => now()->subHour(),
        ]);
    }

    /**
     * Set a specific platform for the session.
     */
    public function platform(string $platform): static
    {
        return $this->state(fn (array $attributes) => [
            'platform' => $platform,
        ]);
    }

    /**
     * Set a specific user for the session.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Set a specific token hash for the session.
     */
    public function withTokenHash(string $tokenHash): static
    {
        return $this->state(fn (array $attributes) => [
            'app_token_hash' => $tokenHash,
        ]);
    }
}
