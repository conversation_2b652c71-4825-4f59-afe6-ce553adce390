<?php

namespace Tests\Feature;

use App\Models\User;
use Firebase\JWT\JWT;
use Tests\RefreshInMemoryDatabase;
use Tests\TestCase;

class TokenExchangeTest extends TestCase
{
    use RefreshInMemoryDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshInMemoryDatabase();

        // Ensure token signing key is set for JWT operations in tests
        config([
            // Use plain base64 (no prefix) to align with controller normalization
            'app.token_signing_key' => base64_encode('test-signing-key-32-characters-long'),
        ]);

        // Bind UNA auth service to a stub that returns a session to avoid external dependency
        $this->app->bind(\App\Services\UnaAuthService::class, function ($app) {
            return new class($app->make(\App\Services\UnaApiService::class)) extends \App\Services\UnaAuthService {
                public function __construct($svc) { parent::__construct($svc); }
                public function getUnaSession(\App\Models\User $user, bool $autoCreate = true): ?\App\Models\UnaSession { return new \App\Models\UnaSession($user, 'test-session', now()->addHour()); }
            };
        });
    }

    public function test_exchange_token_success()
    {
        // Create a user with Okta data
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'okta_user_id' => 'okta_user_123',
            'okta_email' => '<EMAIL>',
            'okta_profile_data' => json_encode(['sub' => 'okta_user_123']),
        ]);

        // Create a valid JWT token
        $now = time();
        $payload = [
            'sub' => 'okta_user_123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'exp' => $now + 60, // 60 seconds expiry
            'iat' => $now,
        ];

        $key = base64_decode(config('app.token_signing_key'));
        $jwtToken = JWT::encode($payload, $key, 'HS256');

        // Make the exchange request
        $response = $this->postJson('/api/exchange-token', [
            'token' => $jwtToken
        ]);

        // Assert successful response
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'session'
                ])
                ->assertJson([
                    'message' => 'Token exchanged successfully'
                ]);

        // Verify the returned token is a valid Sanctum token
        $sessionToken = $response->json('session');
        $this->assertNotEmpty($sessionToken);

        // Test that the token works for authenticated requests
        $userResponse = $this->withHeaders([
            'Authorization' => 'Bearer ' . $sessionToken
        ])->getJson('/api/user');

        $userResponse->assertStatus(200)
                    ->assertJson([
                        'id' => $user->id,
                        'email' => '<EMAIL>',
                        'name' => 'Test User'
                    ]);
    }

    public function test_exchange_token_missing_token()
    {
        $response = $this->postJson('/api/exchange-token', []);

        $response->assertStatus(400)
                ->assertJson([
                    'error' => 'Token is required'
                ]);
    }

    public function test_exchange_token_invalid_token()
    {
        $response = $this->postJson('/api/exchange-token', [
            'token' => 'invalid.jwt.token'
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'error' => 'Invalid token'
                ]);
    }

    public function test_exchange_token_expired_token()
    {
        // Create a user
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'okta_user_id' => 'okta_user_123',
            'okta_email' => '<EMAIL>',
            'okta_profile_data' => json_encode(['sub' => 'okta_user_123']),
        ]);

        // Create an expired JWT token
        $now = time();
        $payload = [
            'sub' => 'okta_user_123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'exp' => $now - 60, // Expired 60 seconds ago
            'iat' => $now - 120,
        ];

        $key = base64_decode(config('app.token_signing_key'));
        $jwtToken = JWT::encode($payload, $key, 'HS256');

        $response = $this->postJson('/api/exchange-token', [
            'token' => $jwtToken
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'error' => 'Token expired'
                ]);
    }

    public function test_exchange_token_user_not_found()
    {
        // Create a valid JWT token for a non-existent user
        $now = time();
        $payload = [
            'sub' => 'non_existent_user',
            'email' => '<EMAIL>',
            'name' => 'Non Existent User',
            'exp' => $now + 60,
            'iat' => $now,
        ];

        $key = base64_decode(config('app.token_signing_key'));
        $jwtToken = JWT::encode($payload, $key, 'HS256');

        $response = $this->postJson('/api/exchange-token', [
            'token' => $jwtToken
        ]);

        $response->assertStatus(404)
                ->assertJson([
                    'error' => 'User not found'
                ]);
    }

    public function test_exchange_token_same_structure_as_dev_login()
    {
        // Create a user
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'okta_user_id' => 'okta_user_123',
            'okta_email' => '<EMAIL>',
        ]);

        // Test dev login response structure
        $devLoginResponse = $this->postJson('/api/dev-login', [
            'email' => '<EMAIL>'
        ]);

        // Create JWT token for exchange
        $now = time();
        $payload = [
            'sub' => 'okta_user_123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'exp' => $now + 60,
            'iat' => $now,
        ];

        $key = base64_decode(config('app.token_signing_key'));
        $jwtToken = JWT::encode($payload, $key, 'HS256');

        // Test exchange token response structure
        $exchangeResponse = $this->postJson('/api/exchange-token', [
            'token' => $jwtToken
        ]);

        // Both should have the same structure
        $devLoginResponse->assertStatus(200)->assertJsonStructure(['message', 'session']);
        $exchangeResponse->assertStatus(200)->assertJsonStructure(['message', 'session']);

        // Both tokens should work for authenticated requests
        $devToken = $devLoginResponse->json('session');
        $exchangeToken = $exchangeResponse->json('session');

        $this->assertNotEmpty($devToken);
        $this->assertNotEmpty($exchangeToken);

        // Both should authenticate successfully
        $devUserResponse = $this->withHeaders(['Authorization' => 'Bearer ' . $devToken])
                               ->getJson('/api/user');
        $exchangeUserResponse = $this->withHeaders(['Authorization' => 'Bearer ' . $exchangeToken])
                                   ->getJson('/api/user');

        $devUserResponse->assertStatus(200);
        $exchangeUserResponse->assertStatus(200);
    }
}
