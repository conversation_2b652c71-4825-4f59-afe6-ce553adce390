<?php

namespace Tests\Unit;

use App\Services\JwtTokenService;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class JwtTokenServiceTest extends TestCase
{
    use RefreshDatabase;

    private JwtTokenService $jwtTokenService;
    private string $testKey;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test signing key
        $this->testKey = base64_encode(random_bytes(32));
        Config::set('app.token_signing_key', 'base64:' . $this->testKey);
        
        $this->jwtTokenService = new JwtTokenService();
    }

    public function test_create_short_lived_token_creates_valid_jwt()
    {
        $userData = [
            'sub' => 'test-sub-123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ];

        $token = $this->jwtTokenService->createShortLivedToken($userData);

        $this->assertIsString($token);
        $this->assertNotEmpty($token);

        // Decode the token to verify its contents
        $key = base64_decode($this->testKey);
        $decoded = JWT::decode($token, new Key($key, 'HS256'));

        $this->assertEquals($userData['sub'], $decoded->sub);
        $this->assertEquals($userData['email'], $decoded->email);
        $this->assertEquals($userData['name'], $decoded->name);
        $this->assertIsInt($decoded->exp);
        $this->assertIsInt($decoded->iat);
        $this->assertEquals(60, $decoded->exp - $decoded->iat); // Default 60 seconds
    }

    public function test_create_short_lived_token_with_custom_expiry()
    {
        $userData = [
            'sub' => 'test-sub-123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ];

        $customExpiry = 300; // 5 minutes
        $token = $this->jwtTokenService->createShortLivedToken($userData, $customExpiry);

        $key = base64_decode($this->testKey);
        $decoded = JWT::decode($token, new Key($key, 'HS256'));

        $this->assertEquals($customExpiry, $decoded->exp - $decoded->iat);
    }

    public function test_decode_token_returns_valid_object()
    {
        $userData = [
            'sub' => 'test-sub-123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ];

        $token = $this->jwtTokenService->createShortLivedToken($userData);
        $decoded = $this->jwtTokenService->decodeToken($token);

        $this->assertIsObject($decoded);
        $this->assertEquals($userData['sub'], $decoded->sub);
        $this->assertEquals($userData['email'], $decoded->email);
        $this->assertEquals($userData['name'], $decoded->name);
    }

    public function test_decode_token_throws_exception_for_invalid_token()
    {
        $this->expectException(\Exception::class);
        $this->jwtTokenService->decodeToken('invalid.jwt.token');
    }

    public function test_is_token_valid_returns_true_for_valid_token()
    {
        $now = time();
        $decoded = (object) [
            'sub' => 'test-sub',
            'exp' => $now + 3600, // Valid for 1 hour
        ];

        $result = $this->jwtTokenService->isTokenValid($decoded);
        $this->assertTrue($result);
    }

    public function test_is_token_valid_returns_false_for_expired_token()
    {
        $now = time();
        $decoded = (object) [
            'sub' => 'test-sub',
            'exp' => $now - 3600, // Expired 1 hour ago
        ];

        $result = $this->jwtTokenService->isTokenValid($decoded);
        $this->assertFalse($result);
    }

    public function test_is_token_valid_returns_false_for_token_without_exp()
    {
        $decoded = (object) [
            'sub' => 'test-sub',
            // No exp field
        ];

        $result = $this->jwtTokenService->isTokenValid($decoded);
        $this->assertFalse($result);
    }

    public function test_decode_and_validate_token_returns_object_for_valid_token()
    {
        $userData = [
            'sub' => 'test-sub-123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ];

        $token = $this->jwtTokenService->createShortLivedToken($userData);
        $result = $this->jwtTokenService->decodeAndValidateToken($token);

        $this->assertIsObject($result);
        $this->assertEquals($userData['sub'], $result->sub);
    }

    public function test_decode_and_validate_token_returns_null_for_invalid_token()
    {
        $result = $this->jwtTokenService->decodeAndValidateToken('invalid.jwt.token');
        $this->assertNull($result);
    }

    public function test_decode_and_validate_token_returns_null_for_expired_token()
    {
        $userData = [
            'sub' => 'test-sub-123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ];

        // Create an expired token
        $token = $this->jwtTokenService->createShortLivedToken($userData, -60); // Expired 60 seconds ago
        $result = $this->jwtTokenService->decodeAndValidateToken($token);

        $this->assertNull($result);
    }

    public function test_create_token_with_custom_payload()
    {
        $customPayload = [
            'user_id' => 123,
            'role' => 'admin',
            'permissions' => ['read', 'write'],
        ];

        $token = $this->jwtTokenService->createToken($customPayload, 3600);

        $key = base64_decode($this->testKey);
        $decoded = JWT::decode($token, new Key($key, 'HS256'));

        $this->assertEquals(123, $decoded->user_id);
        $this->assertEquals('admin', $decoded->role);
        $this->assertEquals(['read', 'write'], $decoded->permissions);
        $this->assertIsInt($decoded->iat);
        $this->assertIsInt($decoded->exp);
    }

    public function test_extract_user_data_returns_correct_data()
    {
        $decodedToken = (object) [
            'sub' => 'test-sub-123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'exp' => time() + 3600,
            'iat' => time(),
        ];

        $userData = $this->jwtTokenService->extractUserData($decodedToken);

        $this->assertEquals([
            'sub' => 'test-sub-123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ], $userData);
    }

    public function test_extract_user_data_handles_missing_fields()
    {
        $decodedToken = (object) [
            'sub' => 'test-sub-123',
            // Missing email and name
        ];

        $userData = $this->jwtTokenService->extractUserData($decodedToken);

        $this->assertEquals([
            'sub' => 'test-sub-123',
            'email' => null,
            'name' => null,
        ], $userData);
    }

    public function test_throws_exception_when_signing_key_not_configured()
    {
        Config::set('app.token_signing_key', '');

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Token signing key is not configured');

        $this->jwtTokenService->createShortLivedToken(['sub' => 'test']);
    }

    public function test_throws_exception_for_invalid_base64_key()
    {
        Config::set('app.token_signing_key', 'invalid-base64!@#');

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Token signing key must be valid base64');

        $this->jwtTokenService->createShortLivedToken(['sub' => 'test']);
    }

    public function test_throws_exception_for_short_key()
    {
        $shortKey = base64_encode('short'); // Less than 32 bytes
        Config::set('app.token_signing_key', 'base64:' . $shortKey);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Token signing key is too short; require >= 32 bytes');

        $this->jwtTokenService->createShortLivedToken(['sub' => 'test']);
    }

    public function test_handles_key_without_base64_prefix()
    {
        Config::set('app.token_signing_key', $this->testKey); // Without "base64:" prefix

        $userData = ['sub' => 'test', 'email' => '<EMAIL>', 'name' => 'Test'];
        $token = $this->jwtTokenService->createShortLivedToken($userData);

        $this->assertIsString($token);
        $this->assertNotEmpty($token);
    }
}
