<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\OktaUserSession as UserSession;
use App\Services\OktaUserService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Tests\RefreshInMemoryDatabase;

class OktaUserSessionEdgeCasesTest extends TestCase
{
    use RefreshInMemoryDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->refreshInMemoryDatabase();
    }

    // Reuse the existing test methods by delegating to the original test class
    public function test_active_user_sessions_with_mixed_active_inactive_sessions() { (new \Tests\Unit\UserSessionEdgeCasesTest())->test_active_user_sessions_with_mixed_active_inactive_sessions(); }
    public function test_get_active_session_for_platform_with_no_sessions() { (new \Tests\Unit\UserSessionEdgeCasesTest())->test_get_active_session_for_platform_with_no_sessions(); }
    public function test_get_active_session_for_platform_with_multiple_platforms() { (new \Tests\Unit\UserSessionEdgeCasesTest())->test_get_active_session_for_platform_with_multiple_platforms(); }
    public function test_has_active_okta_session_with_expired_sessions() { (new \Tests\Unit\UserSessionEdgeCasesTest())->test_has_active_okta_session_with_expired_sessions(); }
    public function test_is_okta_session_valid_with_null_expiry_dates() { (new \Tests\Unit\UserSessionEdgeCasesTest())->test_is_okta_session_valid_with_null_expiry_dates(); }
    public function test_is_okta_session_valid_with_past_expiry_dates() { (new \Tests\Unit\UserSessionEdgeCasesTest())->test_is_okta_session_valid_with_past_expiry_dates(); }
    public function test_update_activity_multiple_times_in_succession() { (new \Tests\Unit\UserSessionEdgeCasesTest())->test_update_activity_multiple_times_in_succession(); }
    public function test_deactivate_on_already_inactive_sessions() { (new \Tests\Unit\UserSessionEdgeCasesTest())->test_deactivate_on_already_inactive_sessions(); }
}

