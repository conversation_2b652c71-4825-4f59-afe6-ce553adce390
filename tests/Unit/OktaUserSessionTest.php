<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\OktaUserSession as UserSession;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class OktaUserSessionTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        // Force SQLite in-memory for these unit tests to avoid external DB dependencies
        config([
            'database.default' => 'sqlite',
            'database.connections.sqlite.database' => ':memory:',
        ]);

        $this->createTables();
    }

    private function createTables(): void
    {
        Schema::dropIfExists('personal_access_tokens');
        Schema::dropIfExists('user_sessions');
        Schema::dropIfExists('users');

        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('okta_user_id')->nullable()->unique();
            $table->string('okta_email')->nullable();
            $table->text('okta_profile_data')->nullable();
            $table->string('una_session_id')->nullable();
            $table->integer('una_account_id')->nullable();
            $table->integer('una_profile_id')->nullable();
            $table->integer('una_content_id')->nullable();
            $table->timestamp('una_expires_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('user_sessions', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('okta_access_token')->nullable();
            $table->text('okta_refresh_token')->nullable();
            $table->text('okta_id_token')->nullable();
            $table->timestamp('okta_expires_at')->nullable();
            $table->string('app_token_hash')->nullable();
            $table->string('platform')->default('mobile');
            $table->string('okta_session_id')->nullable();
            $table->text('okta_user_data')->nullable();
            $table->string('state')->nullable();
            $table->string('code_verifier')->nullable();
            $table->string('code_challenge')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
        });

        Schema::create('personal_access_tokens', function ($table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }

    // Reuse the same tests by referencing UserSession alias
    public function test_user_session_encrypts_okta_tokens() { (new \Tests\Unit\UserSessionTest())->test_user_session_encrypts_okta_tokens(); }
    public function test_is_okta_session_valid_returns_correct_status() { (new \Tests\Unit\UserSessionTest())->test_is_okta_session_valid_returns_correct_status(); }
    public function test_deactivate_sets_session_inactive() { (new \Tests\Unit\UserSessionTest())->test_deactivate_sets_session_inactive(); }
    public function test_update_activity_updates_timestamp() { (new \Tests\Unit\UserSessionTest())->test_update_activity_updates_timestamp(); }
    public function test_scopes_work_correctly() { (new \Tests\Unit\UserSessionTest())->test_scopes_work_correctly(); }
    public function test_user_relationship_works() { (new \Tests\Unit\UserSessionTest())->test_user_relationship_works(); }
}

